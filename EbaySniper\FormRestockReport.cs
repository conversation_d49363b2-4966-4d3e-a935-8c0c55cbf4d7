using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst
{
    /// <summary>
    /// Form for generating and displaying restock transaction reports
    /// </summary>
    public partial class FormRestockReport : XtraForm
    {
        private readonly IPurchaseTrackerRepository _repository;
        private readonly IReportService _reportService;
        private readonly IReportExportService _exportService;
        private readonly IKeywordDataService _keywordDataService;
        private readonly IReportSnapshotStorage _snapshotStorage;
        private List<PurchaseTransaction> _currentTransactions;
        private List<EnrichedTransactionData> _currentEnrichedData;
        private ReportSnapshot _currentReportSnapshot;

        public FormRestockReport() : this(
            new PurchaseTrackerRepository(),
            new KeywordDataService(new List<Keyword2Find>()),
            new FileReportSnapshotStorage(Path.Combine(Application.StartupPath, "Reports")))
        {
        }

        public FormRestockReport(IPurchaseTrackerRepository repository, IKeywordDataService keywordDataService, IReportSnapshotStorage snapshotStorage)
        {
            InitializeComponent();

            // Initialize services with dependency injection
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _keywordDataService = keywordDataService ?? throw new ArgumentNullException(nameof(keywordDataService));
            _snapshotStorage = snapshotStorage ?? throw new ArgumentNullException(nameof(snapshotStorage));
            _reportService = new ReportService(_repository);
            _exportService = new ReportExportService();

            // Set default values
            spinEditDays.Value = 30;
            _currentTransactions = new List<PurchaseTransaction>();
            _currentEnrichedData = new List<EnrichedTransactionData>();

            // Configure grid
            ConfigureEnhancedGrid();
        }

        private void ConfigureEnhancedGrid()
        {
            var gridView = gridControlTransactions.MainView as GridView;
            if (gridView != null)
            {
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ColumnAutoWidth = false;
                gridView.OptionsBehavior.Editable = false;
                gridView.OptionsSelection.MultiSelect = false;

                // Clear existing columns
                gridView.Columns.Clear();

                // Add enhanced columns with keyword data
                AddGridColumn(gridView, "Alias", "Alias", 120, true, 0);
                AddGridColumn(gridView, "Keywords", "Keywords", 150, true, 1);
                AddGridColumn(gridView, "ItemTitle", "Item Title", 200, true, 2);
                AddGridColumn(gridView, "Status", "Status", 80, true, 3);
                AddGridColumn(gridView, "PurchasePrice", "Price", 80, true, 4, "c2");
                AddGridColumn(gridView, "Quantity", "Qty", 50, true, 5);
                AddGridColumn(gridView, "TotalValue", "Total", 80, true, 6, "c2");
                AddGridColumn(gridView, "RequiredQuantity", "Required", 70, true, 7);
                AddGridColumn(gridView, "PurchasedQuantity", "Purchased", 70, true, 8);
                AddGridColumn(gridView, "RemainingQuantity", "Remaining", 70, true, 9);
                AddGridColumn(gridView, "PriceMin", "Min Price", 80, true, 10, "c2");
                AddGridColumn(gridView, "PriceMax", "Max Price", 80, true, 11, "c2");
                AddGridColumn(gridView, "Condition", "Condition", 100, true, 12);
                AddGridColumn(gridView, "PurchaseDate", "Date", 120, true, 13, "g");
                AddGridColumn(gridView, "ItemId", "Item ID", 100, false, 14);
                AddGridColumn(gridView, "KeywordId", "Keyword ID", 100, false, 15);
                AddGridColumn(gridView, "JobId", "Job ID", 100, false, 16);
                AddGridColumn(gridView, "TransactionId", "Transaction ID", 120, false, 17);
                AddGridColumn(gridView, "PaymentMethod", "Payment", 100, false, 18);
                AddGridColumn(gridView, "EbaySiteName", "eBay Site", 80, false, 19);
                AddGridColumn(gridView, "LocatedIn", "Located In", 100, false, 20);
                AddGridColumn(gridView, "Sellers", "Sellers", 150, false, 21);
                AddGridColumn(gridView, "Categories", "Categories", 100, false, 22);
                AddGridColumn(gridView, "ViewName", "View", 80, false, 23);
                AddGridColumn(gridView, "Notes", "Notes", 200, false, 24);
            }
        }

        private void AddGridColumn(GridView gridView, string fieldName, string caption, int width, bool visible, int visibleIndex, string formatString = "")
        {
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.Visible = visible;
            column.VisibleIndex = visible ? visibleIndex : -1;

            if (!string.IsNullOrEmpty(formatString))
            {
                column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
                column.DisplayFormat.FormatString = formatString;
            }
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                btnGenerateReport.Enabled = false;
                btnExport.Enabled = false;

                // Show loading
                lblStatus.Text = "Generating report...";

                var days = (int)spinEditDays.Value;
                var startDate = DateTime.Now.AddDays(-days);
                var endDate = DateTime.Now;

                // Create filter
                var filter = new ReportFilter
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    IncludeCompleted = true,
                    IncludeFailed = true,
                    IncludePending = true
                };

                // Get transactions
                _currentTransactions = (await _repository.GetTransactionsByDateRangeAsync(startDate, endDate)).ToList();

                // Create enriched data with keyword information
                _currentEnrichedData = await CreateEnrichedTransactionDataAsync(_currentTransactions);

                // Bind enriched data to grid
                gridControlTransactions.DataSource = _currentEnrichedData;

                // Update summary
                UpdateSummary();

                // Create and save complete report snapshot
                _currentReportSnapshot = await CreateCompleteReportSnapshotAsync(filter);
                await _snapshotStorage.SaveSnapshotAsync(_currentReportSnapshot);

                lblStatus.Text = $"Report generated successfully. Found {_currentTransactions.Count} transactions.";
                btnExport.Enabled = _currentTransactions.Count > 0;
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error generating report: {ex.Message}";
                XtraMessageBox.Show($"Error generating report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerateReport.Enabled = true;
            }
        }

        private void UpdateSummary()
        {
            if (_currentTransactions == null || !_currentTransactions.Any())
            {
                lblTotalTransactions.Text = "Total Transactions: 0";
                lblTotalAmount.Text = "Total Amount: $0.00";
                lblCompletedTransactions.Text = "Completed: 0";
                lblFailedTransactions.Text = "Failed: 0";
                return;
            }

            var totalTransactions = _currentTransactions.Count;
            var totalAmount = _currentTransactions.Where(t => t.Status == "Completed").Sum(t => t.PurchasePrice * t.Quantity);
            var completedCount = _currentTransactions.Count(t => t.Status == "Completed");
            var failedCount = _currentTransactions.Count(t => t.Status == "Failed");

            lblTotalTransactions.Text = $"Total Transactions: {totalTransactions}";
            lblTotalAmount.Text = $"Total Amount: ${totalAmount:F2}";
            lblCompletedTransactions.Text = $"Completed: {completedCount}";
            lblFailedTransactions.Text = $"Failed: {failedCount}";
        }

        private async void btnExport_Click(object sender, EventArgs e)
        {
            if (_currentReportSnapshot == null || !_currentReportSnapshot.Data.Any())
            {
                XtraMessageBox.Show("No report snapshot available. Please generate a report first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*";
                    saveDialog.DefaultExt = "csv";
                    saveDialog.FileName = $"RestockReport_Complete_{_currentReportSnapshot.GeneratedAt:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        btnExport.Enabled = false;
                        lblStatus.Text = "Exporting complete report from snapshot...";

                        // Export from saved snapshot - completely independent of current data
                        await ExportReportSnapshotToCsvAsync(_currentReportSnapshot, saveDialog.FileName);

                        lblStatus.Text = $"Complete report exported successfully to: {saveDialog.FileName}";
                        XtraMessageBox.Show($"Complete report exported successfully to:\n{saveDialog.FileName}", "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error exporting report: {ex.Message}";
                XtraMessageBox.Show($"Error exporting report: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnExport.Enabled = true;
            }
        }

        private void FormRestockReport_Load(object sender, EventArgs e)
        {
            lblStatus.Text = "Ready. Select number of days and click Generate Report.";
        }

        private void FormRestockReport_FormClosed(object sender, FormClosedEventArgs e)
        {
            // Clean up resources if needed
            _repository?.Dispose();
        }

        private void spinEditDays_EditValueChanged(object sender, EventArgs e)
        {
            // Clear current results when days value changes
            if (_currentTransactions?.Any() == true)
            {
                lblStatus.Text = "Days value changed. Click Generate Report to refresh data.";
            }
        }

        /// <summary>
        /// Creates enriched transaction data by combining transactions with keyword information
        /// </summary>
        private async Task<List<EnrichedTransactionData>> CreateEnrichedTransactionDataAsync(List<PurchaseTransaction> transactions)
        {
            var enrichedData = new List<EnrichedTransactionData>();

            // Get unique keyword IDs
            var keywordIds = transactions.Select(t => t.KeywordId).Distinct().ToList();

            // Get keyword data for all unique IDs
            var keywords = _keywordDataService.GetKeywordsByIds(keywordIds);
            var keywordDict = keywords.ToDictionary(k => k.Id, k => k);

            // Create enriched data for each transaction
            foreach (var transaction in transactions)
            {
                keywordDict.TryGetValue(transaction.KeywordId, out var keyword);
                var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);
                enrichedData.Add(enriched);
            }

            return enrichedData;
        }

        /// <summary>
        /// Creates a complete report snapshot with all current data and grid configuration
        /// </summary>
        private async Task<ReportSnapshot> CreateCompleteReportSnapshotAsync(ReportFilter filter)
        {
            // Capture current grid column configuration
            var columnConfig = CaptureGridColumnState();

            // Get unique keyword IDs from transactions
            var keywordIds = _currentTransactions.Select(t => t.KeywordId).Distinct().ToList();

            // Capture current state of all keywords
            var keywords = _keywordDataService.GetKeywordsByIds(keywordIds);
            var keywordSnapshots = keywords.ToDictionary(k => k.Id, k => KeywordSnapshot.FromKeyword2Find(k));

            // Create combined transaction + keyword snapshot data
            var snapshotData = _currentTransactions.Select(transaction =>
                new TransactionWithKeywordSnapshot
                {
                    Transaction = transaction,
                    KeywordState = keywordSnapshots.GetValueOrDefault(transaction.KeywordId)
                }).ToList();

            // Create complete snapshot
            return new ReportSnapshot
            {
                GeneratedAt = DateTime.Now,
                Filter = filter,
                ColumnConfiguration = columnConfig,
                Data = snapshotData
            };
        }

        /// <summary>
        /// Captures the current grid column configuration
        /// </summary>
        private List<GridColumnSnapshot> CaptureGridColumnState()
        {
            var gridView = gridControlTransactions.MainView as GridView;
            if (gridView == null) return new List<GridColumnSnapshot>();

            return gridView.Columns.Cast<GridColumn>()
                .Select(col => new GridColumnSnapshot
                {
                    FieldName = col.FieldName,
                    Caption = col.Caption,
                    Visible = col.Visible,
                    VisibleIndex = col.VisibleIndex,
                    Width = col.Width
                }).ToList();
        }

        /// <summary>
        /// Exports a report snapshot to CSV with all captured data
        /// </summary>
        private async Task ExportReportSnapshotToCsvAsync(ReportSnapshot snapshot, string filePath)
        {
            var csv = new System.Text.StringBuilder();

            // Build comprehensive header with all available columns
            var headers = new List<string>
            {
                "Generated At", "Report ID", "Alias", "Keywords", "Item Title", "Status",
                "Purchase Price", "Quantity", "Total Value", "Required Quantity", "Purchased Quantity",
                "Remaining Quantity", "Completion %", "Min Price", "Max Price", "Condition",
                "Purchase Date", "Item ID", "Keyword ID", "Job ID", "Transaction ID",
                "Payment Method", "eBay Site", "Located In", "Available To", "Zip",
                "Sellers", "Seller Type", "Categories", "View Name", "Listing Type",
                "Search In Description", "Frequency", "Threads", "Notes"
            };

            csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

            // Export each row with complete transaction + keyword data
            foreach (var item in snapshot.Data)
            {
                var transaction = item.Transaction;
                var keyword = item.KeywordState;

                var row = new List<string>
                {
                    $"\"{snapshot.GeneratedAt:yyyy-MM-dd HH:mm:ss}\"",
                    $"\"{snapshot.ReportId}\"",
                    $"\"{keyword?.Alias ?? ""}\"",
                    $"\"{keyword?.Keywords ?? ""}\"",
                    $"\"{transaction.ItemTitle}\"",
                    $"\"{transaction.Status}\"",
                    $"{transaction.PurchasePrice:F2}",
                    $"{transaction.Quantity}",
                    $"{transaction.PurchasePrice * transaction.Quantity:F2}",
                    $"{keyword?.RequiredQuantity ?? 0}",
                    $"{keyword?.PurchasedQuantity ?? 0}",
                    $"{keyword?.RemainingQuantity ?? 0}",
                    $"{keyword?.CompletionPercentage ?? 0:F1}",
                    $"{keyword?.PriceMin ?? 0:F2}",
                    $"{keyword?.PriceMax ?? 0:F2}",
                    $"\"{keyword?.ConditionString ?? ""}\"",
                    $"\"{transaction.PurchaseDate:yyyy-MM-dd HH:mm:ss}\"",
                    $"\"{transaction.ItemId}\"",
                    $"\"{transaction.KeywordId}\"",
                    $"\"{transaction.JobId}\"",
                    $"\"{transaction.TransactionId}\"",
                    $"\"{transaction.PaymentMethod}\"",
                    $"\"{keyword?.EbaySiteName ?? ""}\"",
                    $"\"{keyword?.LocatedIn ?? ""}\"",
                    $"\"{keyword?.AvailableTo ?? ""}\"",
                    $"\"{keyword?.Zip ?? ""}\"",
                    $"\"{keyword?.SellersString ?? ""}\"",
                    $"\"{keyword?.SellerType ?? ""}\"",
                    $"\"{keyword?.Categories ?? ""}\"",
                    $"\"{keyword?.ViewName ?? ""}\"",
                    $"\"{keyword?.ListingTypeString ?? ""}\"",
                    $"{keyword?.SearchInDescription ?? false}",
                    $"\"{keyword?.Frequency.ToString() ?? ""}\"",
                    $"{keyword?.Threads ?? 0}",
                    $"\"{transaction.Notes}\""
                };

                csv.AppendLine(string.Join(",", row));
            }

            await File.WriteAllTextAsync(filePath, csv.ToString());
        }
    }
}
