using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.Restocker.Models
{
    [TestClass]
    public class ReportSnapshotTests
    {
        [TestMethod]
        public void ReportSnapshot_DefaultConstructor_InitializesProperties()
        {
            // Act
            var snapshot = new ReportSnapshot();

            // Assert
            Assert.IsNotNull(snapshot.ReportId);
            Assert.AreNotEqual(Guid.Empty.ToString(), snapshot.ReportId);
            Assert.IsNotNull(snapshot.ColumnConfiguration);
            Assert.IsNotNull(snapshot.Data);
            Assert.AreEqual(0, snapshot.ColumnConfiguration.Count);
            Assert.AreEqual(0, snapshot.Data.Count);
        }

        [TestMethod]
        public void ReportSnapshot_JsonSerialization_RoundTripSuccessful()
        {
            // Arrange
            var originalSnapshot = CreateTestReportSnapshot();

            // Act
            var json = JsonSerializer.Serialize(originalSnapshot);
            var deserializedSnapshot = JsonSerializer.Deserialize<ReportSnapshot>(json);

            // Assert
            Assert.AreEqual(originalSnapshot.ReportId, deserializedSnapshot.ReportId);
            Assert.AreEqual(originalSnapshot.GeneratedAt, deserializedSnapshot.GeneratedAt);
            Assert.AreEqual(originalSnapshot.ColumnConfiguration.Count, deserializedSnapshot.ColumnConfiguration.Count);
            Assert.AreEqual(originalSnapshot.Data.Count, deserializedSnapshot.Data.Count);
            
            // Verify first column configuration
            var originalCol = originalSnapshot.ColumnConfiguration.First();
            var deserializedCol = deserializedSnapshot.ColumnConfiguration.First();
            Assert.AreEqual(originalCol.FieldName, deserializedCol.FieldName);
            Assert.AreEqual(originalCol.Caption, deserializedCol.Caption);
            Assert.AreEqual(originalCol.Visible, deserializedCol.Visible);
            Assert.AreEqual(originalCol.VisibleIndex, deserializedCol.VisibleIndex);
            Assert.AreEqual(originalCol.Width, deserializedCol.Width);
        }

        [TestMethod]
        public void GridColumnSnapshot_DefaultConstructor_InitializesProperties()
        {
            // Act
            var column = new GridColumnSnapshot();

            // Assert
            Assert.IsNotNull(column.FieldName);
            Assert.IsNotNull(column.Caption);
            Assert.AreEqual(string.Empty, column.FieldName);
            Assert.AreEqual(string.Empty, column.Caption);
            Assert.IsFalse(column.Visible);
            Assert.AreEqual(0, column.VisibleIndex);
            Assert.AreEqual(0, column.Width);
        }

        [TestMethod]
        public void KeywordSnapshot_DefaultConstructor_InitializesProperties()
        {
            // Act
            var keywordSnapshot = new KeywordSnapshot();

            // Assert
            Assert.IsNotNull(keywordSnapshot.KeywordId);
            Assert.IsNotNull(keywordSnapshot.Alias);
            Assert.IsNotNull(keywordSnapshot.Keywords);
            Assert.IsNotNull(keywordSnapshot.Categories);
            Assert.IsNotNull(keywordSnapshot.EbaySiteName);
            Assert.IsNotNull(keywordSnapshot.LocatedIn);
            Assert.IsNotNull(keywordSnapshot.AvailableTo);
            Assert.IsNotNull(keywordSnapshot.Zip);
            Assert.IsNotNull(keywordSnapshot.SellerType);
            Assert.IsNotNull(keywordSnapshot.ViewName);
            Assert.IsNotNull(keywordSnapshot.Condition);
            Assert.IsNotNull(keywordSnapshot.Sellers);
            Assert.IsNotNull(keywordSnapshot.ListingType);
        }

        [TestMethod]
        public void KeywordSnapshot_FromKeyword2Find_CopiesAllProperties()
        {
            // Arrange
            var keyword = CreateTestKeyword2Find();

            // Act
            var snapshot = KeywordSnapshot.FromKeyword2Find(keyword);

            // Assert
            Assert.AreEqual(keyword.Id, snapshot.KeywordId);
            Assert.AreEqual(keyword.Alias, snapshot.Alias);
            Assert.AreEqual(keyword.Kws, snapshot.Keywords);
            Assert.AreEqual(keyword.SearchInDescription, snapshot.SearchInDescription);
            Assert.AreEqual(keyword.PriceMin, snapshot.PriceMin);
            Assert.AreEqual(keyword.PriceMax, snapshot.PriceMax);
            Assert.AreEqual(keyword.Categories4Api, snapshot.Categories);
            Assert.AreEqual(keyword.EbaySiteName, snapshot.EbaySiteName);
            Assert.AreEqual(keyword.LocatedIn, snapshot.LocatedIn);
            Assert.AreEqual(keyword.AvailableTo, snapshot.AvailableTo);
            Assert.AreEqual(keyword.Zip, snapshot.Zip);
            Assert.AreEqual(keyword.SellerType, snapshot.SellerType);
            Assert.AreEqual(keyword.ViewName, snapshot.ViewName);
            Assert.AreEqual(keyword.RequiredQuantity, snapshot.RequiredQuantity);
            Assert.AreEqual(keyword.PurchasedQuantity, snapshot.PurchasedQuantity);
            Assert.IsTrue(snapshot.CapturedAt <= DateTime.Now);
            Assert.IsTrue(snapshot.CapturedAt >= DateTime.Now.AddMinutes(-1));
        }

        [TestMethod]
        public void KeywordSnapshot_FromKeyword2Find_WithNullKeyword_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => 
                KeywordSnapshot.FromKeyword2Find(null));
        }

        [TestMethod]
        public void TransactionWithKeywordSnapshot_DefaultConstructor_InitializesProperties()
        {
            // Act
            var item = new TransactionWithKeywordSnapshot();

            // Assert
            Assert.IsNull(item.Transaction);
            Assert.IsNull(item.KeywordState);
        }

        [TestMethod]
        public void TransactionWithKeywordSnapshot_JsonSerialization_RoundTripSuccessful()
        {
            // Arrange
            var original = new TransactionWithKeywordSnapshot
            {
                Transaction = CreateTestPurchaseTransaction(),
                KeywordState = KeywordSnapshot.FromKeyword2Find(CreateTestKeyword2Find())
            };

            // Act
            var json = JsonSerializer.Serialize(original);
            var deserialized = JsonSerializer.Deserialize<TransactionWithKeywordSnapshot>(json);

            // Assert
            Assert.AreEqual(original.Transaction.Id, deserialized.Transaction.Id);
            Assert.AreEqual(original.Transaction.KeywordId, deserialized.Transaction.KeywordId);
            Assert.AreEqual(original.KeywordState.KeywordId, deserialized.KeywordState.KeywordId);
            Assert.AreEqual(original.KeywordState.Alias, deserialized.KeywordState.Alias);
        }

        private ReportSnapshot CreateTestReportSnapshot()
        {
            return new ReportSnapshot
            {
                ReportId = "test-report-123",
                GeneratedAt = new DateTime(2024, 12, 19, 14, 30, 0),
                Filter = new ReportFilter
                {
                    StartDate = new DateTime(2024, 11, 19),
                    EndDate = new DateTime(2024, 12, 19),
                    IncludeCompleted = true,
                    IncludeFailed = true,
                    IncludePending = false
                },
                ColumnConfiguration = new List<GridColumnSnapshot>
                {
                    new GridColumnSnapshot
                    {
                        FieldName = "ItemTitle",
                        Caption = "Item Title",
                        Visible = true,
                        VisibleIndex = 0,
                        Width = 200
                    },
                    new GridColumnSnapshot
                    {
                        FieldName = "PurchasePrice",
                        Caption = "Price",
                        Visible = true,
                        VisibleIndex = 1,
                        Width = 100
                    }
                },
                Data = new List<TransactionWithKeywordSnapshot>
                {
                    new TransactionWithKeywordSnapshot
                    {
                        Transaction = CreateTestPurchaseTransaction(),
                        KeywordState = KeywordSnapshot.FromKeyword2Find(CreateTestKeyword2Find())
                    }
                }
            };
        }

        private PurchaseTransaction CreateTestPurchaseTransaction()
        {
            return new PurchaseTransaction
            {
                Id = 1,
                KeywordId = "keyword-123",
                JobId = "job-456",
                ItemId = "item-789",
                ItemTitle = "Test Item",
                PurchasePrice = 25.99m,
                Quantity = 2,
                PurchaseDate = new DateTime(2024, 12, 19, 10, 30, 0),
                Status = "Completed",
                TransactionId = "txn-123",
                PaymentMethod = "PayPal",
                ShippingAddress = "123 Test St",
                Notes = "Test purchase"
            };
        }

        private Keyword2Find CreateTestKeyword2Find()
        {
            return new Keyword2Find
            {
                Id = "keyword-123",
                Alias = "Test Product",
                Kws = "test,product,keywords",
                JobId = "job-456",
                RequiredQuantity = 5,
                PurchasedQuantity = 2,
                PriceMin = 10.0,
                PriceMax = 50.0,
                Categories4Api = "12345",
                EbaySiteName = "eBay US",
                LocatedIn = "United States",
                AvailableTo = "Worldwide",
                Zip = "12345",
                SellerType = "Include",
                ViewName = "TestView",
                SearchInDescription = true,
                Condition = new[] { "New", "Used" },
                Sellers = new[] { "seller1", "seller2" },
                ListingType = new[] { "Auction", "BuyItNow" }
            };
        }
    }
}
