using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class ReportSnapshotStorageTests
    {
        private IReportSnapshotStorage _storage;
        private string _testDirectory;

        [TestInitialize]
        public void Setup()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "RestockReportTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            _storage = new FileReportSnapshotStorage(_testDirectory);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        [TestMethod]
        public async Task SaveSnapshotAsync_WithValidSnapshot_SavesFileSuccessfully()
        {
            // Arrange
            var snapshot = CreateTestReportSnapshot();

            // Act
            await _storage.SaveSnapshotAsync(snapshot);

            // Assert
            var files = Directory.GetFiles(_testDirectory, "*.json");
            Assert.AreEqual(1, files.Length);
            
            var fileName = Path.GetFileName(files[0]);
            Assert.IsTrue(fileName.StartsWith($"RestockReport_{snapshot.GeneratedAt:yyyyMMdd_HHmmss}"));
            Assert.IsTrue(fileName.EndsWith(".json"));
        }

        [TestMethod]
        public async Task SaveSnapshotAsync_WithNullSnapshot_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(() => 
                _storage.SaveSnapshotAsync(null));
        }

        [TestMethod]
        public async Task LoadSnapshotAsync_WithValidReportId_ReturnsSnapshot()
        {
            // Arrange
            var originalSnapshot = CreateTestReportSnapshot();
            await _storage.SaveSnapshotAsync(originalSnapshot);

            // Act
            var loadedSnapshot = await _storage.LoadSnapshotAsync(originalSnapshot.ReportId);

            // Assert
            Assert.IsNotNull(loadedSnapshot);
            Assert.AreEqual(originalSnapshot.ReportId, loadedSnapshot.ReportId);
            Assert.AreEqual(originalSnapshot.GeneratedAt, loadedSnapshot.GeneratedAt);
            Assert.AreEqual(originalSnapshot.ColumnConfiguration.Count, loadedSnapshot.ColumnConfiguration.Count);
            Assert.AreEqual(originalSnapshot.Data.Count, loadedSnapshot.Data.Count);
        }

        [TestMethod]
        public async Task LoadSnapshotAsync_WithInvalidReportId_ReturnsNull()
        {
            // Act
            var result = await _storage.LoadSnapshotAsync("non-existent-report-id");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task LoadSnapshotAsync_WithNullReportId_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(() => 
                _storage.LoadSnapshotAsync(null));
        }

        [TestMethod]
        public async Task LoadSnapshotAsync_WithEmptyReportId_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(() => 
                _storage.LoadSnapshotAsync(string.Empty));
        }

        [TestMethod]
        public async Task GetAllSnapshotsAsync_WithMultipleSnapshots_ReturnsAllSnapshots()
        {
            // Arrange
            var snapshot1 = CreateTestReportSnapshot();
            var snapshot2 = CreateTestReportSnapshot();
            snapshot2.ReportId = "different-report-id";
            snapshot2.GeneratedAt = DateTime.Now.AddHours(-1);

            await _storage.SaveSnapshotAsync(snapshot1);
            await _storage.SaveSnapshotAsync(snapshot2);

            // Act
            var allSnapshots = await _storage.GetAllSnapshotsAsync();

            // Assert
            Assert.AreEqual(2, allSnapshots.Count);
            Assert.IsTrue(allSnapshots.Any(s => s.ReportId == snapshot1.ReportId));
            Assert.IsTrue(allSnapshots.Any(s => s.ReportId == snapshot2.ReportId));
        }

        [TestMethod]
        public async Task GetAllSnapshotsAsync_WithNoSnapshots_ReturnsEmptyList()
        {
            // Act
            var allSnapshots = await _storage.GetAllSnapshotsAsync();

            // Assert
            Assert.IsNotNull(allSnapshots);
            Assert.AreEqual(0, allSnapshots.Count);
        }

        [TestMethod]
        public async Task GetAllSnapshotsAsync_OrdersByGeneratedAtDescending()
        {
            // Arrange
            var snapshot1 = CreateTestReportSnapshot();
            snapshot1.GeneratedAt = new DateTime(2024, 12, 19, 10, 0, 0);
            
            var snapshot2 = CreateTestReportSnapshot();
            snapshot2.ReportId = "newer-report";
            snapshot2.GeneratedAt = new DateTime(2024, 12, 19, 12, 0, 0);
            
            var snapshot3 = CreateTestReportSnapshot();
            snapshot3.ReportId = "oldest-report";
            snapshot3.GeneratedAt = new DateTime(2024, 12, 19, 8, 0, 0);

            await _storage.SaveSnapshotAsync(snapshot1);
            await _storage.SaveSnapshotAsync(snapshot2);
            await _storage.SaveSnapshotAsync(snapshot3);

            // Act
            var allSnapshots = await _storage.GetAllSnapshotsAsync();

            // Assert
            Assert.AreEqual(3, allSnapshots.Count);
            Assert.AreEqual("newer-report", allSnapshots[0].ReportId); // Most recent first
            Assert.AreEqual(snapshot1.ReportId, allSnapshots[1].ReportId);
            Assert.AreEqual("oldest-report", allSnapshots[2].ReportId); // Oldest last
        }

        [TestMethod]
        public async Task SaveSnapshotAsync_CreatesDirectoryIfNotExists()
        {
            // Arrange
            var nonExistentDirectory = Path.Combine(Path.GetTempPath(), "NonExistentReportDir", Guid.NewGuid().ToString());
            var storage = new FileReportSnapshotStorage(nonExistentDirectory);
            var snapshot = CreateTestReportSnapshot();

            try
            {
                // Act
                await storage.SaveSnapshotAsync(snapshot);

                // Assert
                Assert.IsTrue(Directory.Exists(nonExistentDirectory));
                var files = Directory.GetFiles(nonExistentDirectory, "*.json");
                Assert.AreEqual(1, files.Length);
            }
            finally
            {
                if (Directory.Exists(nonExistentDirectory))
                {
                    Directory.Delete(nonExistentDirectory, true);
                }
            }
        }

        [TestMethod]
        public void Constructor_WithNullDirectory_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => 
                new FileReportSnapshotStorage(null));
        }

        [TestMethod]
        public void Constructor_WithEmptyDirectory_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => 
                new FileReportSnapshotStorage(string.Empty));
        }

        private ReportSnapshot CreateTestReportSnapshot()
        {
            return new ReportSnapshot
            {
                ReportId = "test-report-" + Guid.NewGuid().ToString("N")[..8],
                GeneratedAt = new DateTime(2024, 12, 19, 14, 30, 0),
                Filter = new ReportFilter
                {
                    StartDate = new DateTime(2024, 11, 19),
                    EndDate = new DateTime(2024, 12, 19),
                    IncludeCompleted = true,
                    IncludeFailed = true,
                    IncludePending = false
                },
                ColumnConfiguration = new List<GridColumnSnapshot>
                {
                    new GridColumnSnapshot
                    {
                        FieldName = "ItemTitle",
                        Caption = "Item Title",
                        Visible = true,
                        VisibleIndex = 0,
                        Width = 200
                    }
                },
                Data = new List<TransactionWithKeywordSnapshot>
                {
                    new TransactionWithKeywordSnapshot
                    {
                        Transaction = new PurchaseTransaction
                        {
                            Id = 1,
                            KeywordId = "keyword-123",
                            JobId = "job-456",
                            ItemId = "item-789",
                            ItemTitle = "Test Item",
                            PurchasePrice = 25.99m,
                            Quantity = 2,
                            Status = "Completed"
                        },
                        KeywordState = new KeywordSnapshot
                        {
                            KeywordId = "keyword-123",
                            Alias = "Test Product",
                            Keywords = "test,product",
                            RequiredQuantity = 5,
                            PurchasedQuantity = 2
                        }
                    }
                }
            };
        }
    }
}
